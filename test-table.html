<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12306余票监控测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .ticket-info {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .number {
            color: #0066cc;
            font-weight: bold;
        }
        
        .time {
            color: #666;
        }
        
        .seat-available {
            color: #28a745;
            font-weight: bold;
        }
        
        .seat-unavailable {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>12306余票监控测试页面</h1>
    <p>这个页面用于测试列头红点功能。点击列头的小红点可以高亮对应的列。</p>
    
    <div class="ticket-info">
        <table>
            <thead>
                <tr>
                    <th>车次</th>
                    <th>出发时间</th>
                    <th>到达时间</th>
                    <th>历时</th>
                    <th>商务座</th>
                    <th>一等座</th>
                    <th>二等座</th>
                    <th>硬卧</th>
                    <th>硬座</th>
                    <th>无座</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="number">G1</span></td>
                    <td class="time">08:00</td>
                    <td class="time">14:30</td>
                    <td>6小时30分</td>
                    <td class="seat-available" aria-label="G1次列车，商务座票价1200元，余票15">15</td>
                    <td class="seat-available" aria-label="G1次列车，一等座票价800元，余票25">25</td>
                    <td class="seat-available" aria-label="G1次列车，二等座票价500元，余票50">50</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-available" aria-label="G1次列车，无座票价500元，余票10">10</td>
                </tr>
                <tr>
                    <td><span class="number">K268</span></td>
                    <td class="time">20:15</td>
                    <td class="time">07:45</td>
                    <td>11小时30分</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-available" aria-label="K268次列车，硬卧票价280元，余票8">8</td>
                    <td class="seat-available" aria-label="K268次列车，硬座票价150元，余票30">30</td>
                    <td class="seat-available" aria-label="K268次列车，无座票价150元，余票20">20</td>
                </tr>
                <tr>
                    <td><span class="number">Z162</span></td>
                    <td class="time">22:30</td>
                    <td class="time">08:15</td>
                    <td>9小时45分</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-unavailable">--</td>
                    <td class="seat-available" aria-label="Z162次列车，硬卧票价320元，余票12">12</td>
                    <td class="seat-available" aria-label="Z162次列车，硬座票价180元，余票40">40</td>
                    <td class="seat-available" aria-label="Z162次列车，无座票价180元，余票15">15</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <script>
        // 模拟用户脚本的核心功能
        let activeColumns = new Set();

        function addRedDotsToHeaders() {
            const headerCells = document.querySelectorAll('.ticket-info thead tr th');
            
            headerCells.forEach((cell, index) => {
                if (cell.querySelector('.red-dot')) return;
                
                const redDot = document.createElement('span');
                redDot.className = 'red-dot';
                redDot.style.cssText = `
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    background-color: #ff4444;
                    border-radius: 50%;
                    margin-left: 8px;
                    cursor: pointer;
                    opacity: 0.7;
                    transition: all 0.3s ease;
                    border: 1px solid #fff;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                    vertical-align: middle;
                `;
                
                redDot.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleColumnHighlight(index);
                });
                
                redDot.addEventListener('mouseenter', function() {
                    this.style.opacity = '1';
                    this.style.transform = 'scale(1.2)';
                });
                
                redDot.addEventListener('mouseleave', function() {
                    this.style.opacity = activeColumns.has(index) ? '1' : '0.7';
                    this.style.transform = 'scale(1)';
                });
                
                cell.appendChild(redDot);
            });
        }

        function toggleColumnHighlight(columnIndex) {
            if (activeColumns.has(columnIndex)) {
                activeColumns.delete(columnIndex);
                removeColumnHighlight(columnIndex);
            } else {
                activeColumns.add(columnIndex);
                highlightColumn(columnIndex);
            }
            updateRedDotStates();
        }

        function highlightColumn(columnIndex) {
            const allRows = document.querySelectorAll('.ticket-info tr');
            allRows.forEach(row => {
                const cells = row.querySelectorAll('td, th');
                if (cells[columnIndex]) {
                    cells[columnIndex].style.backgroundColor = 'rgba(255, 68, 68, 0.3)';
                    cells[columnIndex].style.border = '2px solid #ff4444';
                    cells[columnIndex].style.boxShadow = '0 0 5px rgba(255, 68, 68, 0.5)';
                }
            });
        }

        function removeColumnHighlight(columnIndex) {
            const allRows = document.querySelectorAll('.ticket-info tr');
            allRows.forEach(row => {
                const cells = row.querySelectorAll('td, th');
                if (cells[columnIndex]) {
                    cells[columnIndex].style.backgroundColor = '';
                    cells[columnIndex].style.border = '';
                    cells[columnIndex].style.boxShadow = '';
                }
            });
        }

        function updateRedDotStates() {
            const redDots = document.querySelectorAll('.red-dot');
            redDots.forEach((dot, index) => {
                if (activeColumns.has(index)) {
                    dot.style.opacity = '1';
                    dot.style.backgroundColor = '#ff0000';
                } else {
                    dot.style.opacity = '0.7';
                    dot.style.backgroundColor = '#ff4444';
                }
            });
        }

        // 页面加载完成后添加红点
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addRedDotsToHeaders();
            }, 100);
        });
    </script>
</body>
</html>
